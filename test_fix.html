<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript加载时机修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>JavaScript加载时机修复测试</h1>
    
    <div class="test-section info">
        <h3>测试说明</h3>
        <p>此页面用于测试修复后的JavaScript加载时机问题。主要测试以下功能：</p>
        <ul>
            <li>区域隔声量对比页面的车型下拉框初始化</li>
            <li>气密性图片页面的车型下拉框初始化</li>
            <li>AJAX标签页加载时的脚本执行时机</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>测试操作</h3>
        <button onclick="testAreaComparison()">测试区域隔声量对比页面</button>
        <button onclick="testAirtightnessImages()">测试气密性图片页面</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <h3>测试日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function testAreaComparison() {
            log('开始测试区域隔声量对比页面...');
            
            // 模拟打开标签页
            const url = 'http://127.0.0.1:5000/sound_insulation/area_comparison?ajax=1';
            
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(content => {
                log('页面内容加载成功');
                
                // 创建临时容器测试内容
                const testContainer = document.createElement('div');
                testContainer.innerHTML = content;
                document.body.appendChild(testContainer);
                
                // 检查关键元素是否存在
                const areaSelect = testContainer.querySelector('#area-select');
                const vehicleMultiselect = testContainer.querySelector('#vehicle-multiselect');
                
                if (areaSelect && vehicleMultiselect) {
                    log('✓ 关键DOM元素存在', 'success');
                    
                    // 执行脚本
                    const scripts = testContainer.querySelectorAll('script');
                    log(`发现 ${scripts.length} 个脚本标签`);
                    
                    scripts.forEach((script, index) => {
                        if (script.textContent.includes('initSoundInsulationPage')) {
                            log(`✓ 找到初始化脚本 (脚本 ${index + 1})`, 'success');
                        }
                    });
                    
                } else {
                    log('✗ 关键DOM元素缺失', 'error');
                }
                
                // 清理测试容器
                setTimeout(() => {
                    document.body.removeChild(testContainer);
                    log('测试容器已清理');
                }, 2000);
                
            })
            .catch(error => {
                log(`✗ 测试失败: ${error.message}`, 'error');
            });
        }

        function testAirtightnessImages() {
            log('开始测试气密性图片页面...');
            
            // 模拟打开标签页
            const url = 'http://127.0.0.1:5000/airtightness/images?ajax=1';
            
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(content => {
                log('页面内容加载成功');
                
                // 创建临时容器测试内容
                const testContainer = document.createElement('div');
                testContainer.innerHTML = content;
                document.body.appendChild(testContainer);
                
                // 检查关键元素是否存在
                const vehicleSelect = testContainer.querySelector('#vehicle-select');
                const viewImagesBtn = testContainer.querySelector('#view-images-btn');
                
                if (vehicleSelect && viewImagesBtn) {
                    log('✓ 关键DOM元素存在', 'success');
                    
                    // 执行脚本
                    const scripts = testContainer.querySelectorAll('script');
                    log(`发现 ${scripts.length} 个脚本标签`);
                    
                    scripts.forEach((script, index) => {
                        if (script.textContent.includes('initAirtightnessPage')) {
                            log(`✓ 找到初始化脚本 (脚本 ${index + 1})`, 'success');
                        }
                    });
                    
                } else {
                    log('✗ 关键DOM元素缺失', 'error');
                }
                
                // 清理测试容器
                setTimeout(() => {
                    document.body.removeChild(testContainer);
                    log('测试容器已清理');
                }, 2000);
                
            })
            .catch(error => {
                log(`✗ 测试失败: ${error.message}`, 'error');
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成');
            log('可以开始测试修复后的JavaScript加载时机');
        });
    </script>
</body>
</html>
