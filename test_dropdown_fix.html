<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车型下拉框修复测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .test-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">
            <i class="fas fa-bug me-2"></i>车型下拉框JavaScript加载时机修复测试
        </h1>

        <div class="test-section">
            <h3><i class="fas fa-info-circle me-2"></i>测试说明</h3>
            <p>此测试页面用于验证修复后的JavaScript加载时机问题，主要测试：</p>
            <ul>
                <li><strong>区域隔声量对比页面</strong>：车型多选下拉框是否在首次加载时正确初始化</li>
                <li><strong>气密性图片页面</strong>：车型选择下拉框是否在首次加载时正确填充选项</li>
                <li><strong>AJAX标签页加载</strong>：脚本执行时机是否正确</li>
            </ul>
            <div class="alert alert-info">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>预期结果：</strong>页面首次加载时，车型下拉框应该立即显示可用选项，无需手动刷新页面。
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-play me-2"></i>测试操作</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary w-100 mb-2" onclick="testAreaComparison()">
                        <i class="fas fa-chart-line me-2"></i>测试区域隔声量对比
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-success w-100 mb-2" onclick="testAirtightnessImages()">
                        <i class="fas fa-images me-2"></i>测试气密性图片页面
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-info w-100 mb-2" onclick="openMainApp()">
                        <i class="fas fa-external-link-alt me-2"></i>打开主应用
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-secondary w-100 mb-2" onclick="clearLog()">
                        <i class="fas fa-trash me-2"></i>清空日志
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-list me-2"></i>测试状态</h3>
            <div id="test-status">
                <div class="mb-2">
                    <span class="status-indicator status-info"></span>
                    <span>等待测试开始...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-terminal me-2"></i>测试日志</h3>
            <div id="log-container" class="log-container">
                <div>测试日志将在这里显示...</div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-desktop me-2"></i>应用预览</h3>
            <iframe id="app-frame" class="test-frame" src="about:blank"></iframe>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testResults = {
            areaComparison: null,
            airtightnessImages: null
        };

        function log(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            
            if (type === 'error') {
                logEntry.style.color = '#dc3545';
            } else if (type === 'success') {
                logEntry.style.color = '#28a745';
            } else if (type === 'warning') {
                logEntry.style.color = '#ffc107';
            }
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log-container').innerHTML = '<div>日志已清空...</div>';
        }

        function updateTestStatus() {
            const statusContainer = document.getElementById('test-status');
            let html = '';
            
            // 区域隔声量对比测试状态
            const areaStatus = testResults.areaComparison;
            let areaClass = 'status-info';
            let areaText = '未测试';
            
            if (areaStatus === true) {
                areaClass = 'status-success';
                areaText = '测试通过';
            } else if (areaStatus === false) {
                areaClass = 'status-error';
                areaText = '测试失败';
            }
            
            html += `<div class="mb-2"><span class="status-indicator ${areaClass}"></span>区域隔声量对比页面: ${areaText}</div>`;
            
            // 气密性图片测试状态
            const imagesStatus = testResults.airtightnessImages;
            let imagesClass = 'status-info';
            let imagesText = '未测试';
            
            if (imagesStatus === true) {
                imagesClass = 'status-success';
                imagesText = '测试通过';
            } else if (imagesStatus === false) {
                imagesClass = 'status-error';
                imagesText = '测试失败';
            }
            
            html += `<div class="mb-2"><span class="status-indicator ${imagesClass}"></span>气密性图片页面: ${imagesText}</div>`;
            
            statusContainer.innerHTML = html;
        }

        function testAreaComparison() {
            log('开始测试区域隔声量对比页面...');
            testResults.areaComparison = null;
            updateTestStatus();
            
            const url = 'http://127.0.0.1:5000/sound_insulation/area_comparison?ajax=1';
            
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(content => {
                log('✓ 页面内容加载成功', 'success');
                
                // 检查关键元素
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;
                
                const areaSelect = tempDiv.querySelector('#area-select');
                const vehicleMultiselect = tempDiv.querySelector('#vehicle-multiselect');
                const generateBtn = tempDiv.querySelector('#generate-comparison-btn');
                
                if (areaSelect && vehicleMultiselect && generateBtn) {
                    log('✓ 关键DOM元素检查通过', 'success');
                    testResults.areaComparison = true;
                } else {
                    log('✗ 关键DOM元素缺失', 'error');
                    testResults.areaComparison = false;
                }
                
                updateTestStatus();
            })
            .catch(error => {
                log(`✗ 测试失败: ${error.message}`, 'error');
                testResults.areaComparison = false;
                updateTestStatus();
            });
        }

        function testAirtightnessImages() {
            log('开始测试气密性图片页面...');
            testResults.airtightnessImages = null;
            updateTestStatus();
            
            const url = 'http://127.0.0.1:5000/airtightness/images?ajax=1';
            
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(content => {
                log('✓ 页面内容加载成功', 'success');
                
                // 检查关键元素
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;
                
                const vehicleSelect = tempDiv.querySelector('#vehicle-select');
                const viewImagesBtn = tempDiv.querySelector('#view-images-btn');
                const openNewWindowBtn = tempDiv.querySelector('#open-new-window-btn');
                
                if (vehicleSelect && viewImagesBtn && openNewWindowBtn) {
                    log('✓ 关键DOM元素检查通过', 'success');
                    testResults.airtightnessImages = true;
                } else {
                    log('✗ 关键DOM元素缺失', 'error');
                    testResults.airtightnessImages = false;
                }
                
                updateTestStatus();
            })
            .catch(error => {
                log(`✗ 测试失败: ${error.message}`, 'error');
                testResults.airtightnessImages = false;
                updateTestStatus();
            });
        }

        function openMainApp() {
            log('打开主应用进行实际测试...');
            const frame = document.getElementById('app-frame');
            frame.src = 'http://127.0.0.1:5000';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成，可以开始测试');
            updateTestStatus();
        });
    </script>
</body>
</html>
